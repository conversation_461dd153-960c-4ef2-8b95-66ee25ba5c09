import { Test, TestingModule } from '@nestjs/testing';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import {
  CustomImportConfigDto,
  TaxRulesDto,
} from '@business-base/application/dto/customer-preferences.dto';
import { S3Service } from '@common/s3/s3.service';
import { SQSService } from '@common/sqs/sqs.service';
import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';
import { BusinessException, BusinessExceptionStatus } from '@common/exception/types/BusinessException';

describe('PortfolioUseCase - Tax Rules Calculations', () => {
  let useCase: PortfolioUseCase;

  // Mock all dependencies
  const mockPortfolioAdapter = { create: jest.fn(), get: jest.fn() };
  const mockPortfolioItemAdapter = { create: jest.fn(), get: jest.fn() };
  const mockCustomerAdapter = { get: jest.fn() };
  const mockPortfolioItemImportErrorAdapter = { create: jest.fn(), get: jest.fn() };
  const mockS3Service = { uploadFileMultPart: jest.fn(), getObjectStream: jest.fn() };
  const mockSqsService = { getImportItemQueueByCustomer: jest.fn(), sendBatch: jest.fn() };
  const mockCustomerPreferencesUseCase = { getByCustomerId: jest.fn() };
  const mockPortfolioItemUseCase = { create: jest.fn() };
  const mockPortfolioItemCustomDataAdapter = { create: jest.fn(), get: jest.fn() };
  const mockMiddlewareResponseOutputAdapter = { create: jest.fn(), get: jest.fn() };
  const mockInfraWorkflowAdapter = { create: jest.fn(), get: jest.fn() };
  const mockCollectCashStatsAdapter = {
    getTotaldealValueByPortfolioId: jest.fn(),
    getTotaldealValueByPortfolioIdWithDateRange: jest.fn(),
    getTotalDealValueByCustomerId: jest.fn(),
    getTotalDealValueByCustomerIdWithDateRange: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PortfolioUseCase,
        { provide: 'PortfolioPort', useValue: mockPortfolioAdapter },
        { provide: 'PortfolioItemPort', useValue: mockPortfolioItemAdapter },
        { provide: 'CustomerPort', useValue: mockCustomerAdapter },
        { provide: 'PortfolioItemImportErrorPort', useValue: mockPortfolioItemImportErrorAdapter },
        { provide: S3Service, useValue: mockS3Service },
        { provide: SQSService, useValue: mockSqsService },
        { provide: CustomerPreferencesUseCase, useValue: mockCustomerPreferencesUseCase },
        { provide: PortfolioItemUseCase, useValue: mockPortfolioItemUseCase },
        { provide: 'PortfolioItemCustomDataPort', useValue: mockPortfolioItemCustomDataAdapter },
        { provide: 'MiddlewareResponseOutputPort', useValue: mockMiddlewareResponseOutputAdapter },
        { provide: 'InfraWorkflowPort', useValue: mockInfraWorkflowAdapter },
        { provide: 'CollectCashStatsPort', useValue: mockCollectCashStatsAdapter },
      ],
    }).compile();

    useCase = module.get<PortfolioUseCase>(PortfolioUseCase);
  });

  describe('calculatePenaltyAmount', () => {
    it('should calculate penalty amount correctly with valid inputs', () => {
      // Test case: R$ 1000.00 with 2.69% penalty fee
      const originalValue = 1000.0;
      const penaltyFee = 0.0269; // 2.69% as decimal

      // Expected: 1000 * 0.0269 = 26.90
      const result = (useCase as any).calculatePenaltyAmount(originalValue, penaltyFee);
      const expectedCents = Math.round(26.9 * 100); // 2690 cents

      expect(result).toBe(expectedCents);
      expect(result / 100).toBeCloseTo(26.9, 2);
    });

    it('should return 0 when originalValue is null', () => {
      const result = (useCase as any).calculatePenaltyAmount(null, 0.0269);
      expect(result).toBe(0);
    });

    it('should return 0 when originalValue is undefined', () => {
      const result = (useCase as any).calculatePenaltyAmount(undefined, 0.0269);
      expect(result).toBe(0);
    });

    it('should return 0 when penaltyFee is null', () => {
      const result = (useCase as any).calculatePenaltyAmount(1000.0, null);
      expect(result).toBe(0);
    });

    it('should return 0 when penaltyFee is undefined', () => {
      const result = (useCase as any).calculatePenaltyAmount(1000.0, undefined);
      expect(result).toBe(0);
    });

    it('should handle edge case with zero penalty fee', () => {
      const result = (useCase as any).calculatePenaltyAmount(1000.0, 0);
      expect(result).toBe(0);
    });

    it('should handle high precision calculations', () => {
      // Test with high precision values
      const originalValue = 1234.56;
      const penaltyFee = 0.123456; // 12.3456%

      const result = (useCase as any).calculatePenaltyAmount(originalValue, penaltyFee);
      const expectedAmount = 1234.56 * 0.123456;

      expect(result / 100).toBeCloseTo(expectedAmount, 2);
    });
  });

  describe('calculateInterestCharges', () => {
    it('should calculate interest charges correctly with valid inputs', () => {
      // Test case: R$ 1000.00 with 0.0003333 daily fee for 30 days
      const originalValue = 1000.0;
      const dailyFee = 0.0003333; // Daily rate
      const overdueDays = 30;

      // Expected: 1000 * 0.0003333 * 30 = 9.999 ≈ 10.00
      const result = (useCase as any).calculateInterestCharges(
        originalValue,
        dailyFee,
        overdueDays,
      );
      const expectedAmount = 1000.0 * 0.0003333 * 30;

      expect(result / 100).toBeCloseTo(expectedAmount, 2);
    });

    it('should return 0 when originalValue is null', () => {
      const result = (useCase as any).calculateInterestCharges(null, 0.0003333, 30);
      expect(result).toBe(0);
    });

    it('should return 0 when dailyFee is null', () => {
      const result = (useCase as any).calculateInterestCharges(1000.0, null, 30);
      expect(result).toBe(0);
    });

    it('should return 0 when overdueDays is null', () => {
      const result = (useCase as any).calculateInterestCharges(1000.0, 0.0003333, null);
      expect(result).toBe(0);
    });

    it('should handle zero overdue days', () => {
      const result = (useCase as any).calculateInterestCharges(1000.0, 0.0003333, 0);
      expect(result).toBe(0);
    });

    it('should handle high overdue days', () => {
      const originalValue = 1000.0;
      const dailyFee = 0.0003333;
      const overdueDays = 365; // 1 year

      const result = (useCase as any).calculateInterestCharges(
        originalValue,
        dailyFee,
        overdueDays,
      );
      const expectedAmount = 1000.0 * 0.0003333 * 365;

      expect(result / 100).toBeCloseTo(expectedAmount, 2);
    });
  });

  describe('calculateTotalAmount', () => {
    it('should calculate total amount correctly with valid inputs', () => {
      const originalValue = 1000.0;
      const penaltyAmountCents = 2690; // R$ 26.90
      const interestChargesCents = 1000; // R$ 10.00

      // Expected: 1000.00 + 26.90 + 10.00 = 1036.90
      const result = (useCase as any).calculateTotalAmount(
        originalValue,
        penaltyAmountCents,
        interestChargesCents,
      );
      const expectedCents = Math.round(1036.9 * 100);

      expect(result).toBe(expectedCents);
      expect(result / 100).toBeCloseTo(1036.9, 2);
    });

    it('should return 0 when originalValue is null', () => {
      const result = (useCase as any).calculateTotalAmount(null, 2690, 1000);
      expect(result).toBe(0);
    });

    it('should return 0 when originalValue is undefined', () => {
      const result = (useCase as any).calculateTotalAmount(undefined, 2690, 1000);
      expect(result).toBe(0);
    });

    it('should handle zero penalty and interest charges', () => {
      const originalValue = 1000.0;
      const result = (useCase as any).calculateTotalAmount(originalValue, 0, 0);
      const expectedCents = Math.round(1000.0 * 100);

      expect(result).toBe(expectedCents);
    });
  });

  describe('calculateCustomImportConfigTaxRules - Integration Tests', () => {
    const portfolioId = 'test-portfolio-id';

    it('should calculate all tax rules correctly with complete valid data', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        DIAS_EM_ATRASO: '30',
        PERCENTUAL_MULTA: 2.69, // 2.69%
        PERCENTUAL_JUROS_DIARIOS: 0.0003333, // Daily rate
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '2,69',
          dailyFee: '0,0003333',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.PERCENTUAL_MULTA).toBe(2.69);
      expect(result.PERCENTUAL_JUROS_DIARIOS).toBe(0.0003333);

      // Mathematical validation with ±0.01 tolerance
      const expectedPenalty = 1000.0 * (2.69 / 100); // 26.90
      const expectedInterest = 1000.0 * (0.0003333 / 100) * 30; // ~0.1 (dailyFee is converted from percentage)
      const expectedTotal = 1000.0 + expectedPenalty + expectedInterest; // ~1026.90

      expect(parseFloat(result.VALOR_TOTAL_MULTA)).toBeCloseTo(expectedPenalty, 2);
      expect(parseFloat(result.VALOR_TOTAL_ENCARGOS)).toBeCloseTo(expectedInterest, 2);
      expect(parseFloat(result.VALOR_TOTAL_CORRIGIDO)).toBeCloseTo(expectedTotal, 2);
    });

    it('should handle missing tax rules gracefully', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        DIAS_EM_ATRASO: '30',
        PERCENTUAL_MULTA: 2.69,
        PERCENTUAL_JUROS_DIARIOS: 0.0003333,
      };

      const customImportConfig: CustomImportConfigDto = {}; // No tax rules

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();

      // Should have only the original value
      expect(result.VALOR_ORIGINAL_DA_DIVIDA).toBe('1000,00');
      expect(result.VALOR_TOTAL_MULTA).toBeUndefined();
      expect(result.VALOR_TOTAL_ENCARGOS).toBeUndefined();
      expect(result.VALOR_TOTAL_CORRIGIDO).toBeUndefined();
    });

    it('should handle missing required calculation fields', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        // Missing DIAS_EM_ATRASO, PERCENTUAL_MULTA, PERCENTUAL_JUROS_DIARIOS
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '2,69',
          dailyFee: '0,0003333',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.PERCENTUAL_MULTA).toBe(2.69);
      expect(result.PERCENTUAL_JUROS_DIARIOS).toBe(0.0003333);

      // Should not have calculated values due to missing fields
      expect(result.VALOR_TOTAL_MULTA).toBeUndefined();
      expect(result.VALOR_TOTAL_ENCARGOS).toBeUndefined();
      expect(result.VALOR_TOTAL_CORRIGIDO).toBeUndefined();
    });

    it('should handle edge case with zero values', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '0,00',
        DIAS_EM_ATRASO: '0',
        PERCENTUAL_MULTA: 0,
        PERCENTUAL_JUROS_DIARIOS: 0,
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '0',
          dailyFee: '0',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.VALOR_TOTAL_MULTA).toBe('0.00');
      expect(result.VALOR_TOTAL_ENCARGOS).toBe('0.00');
      expect(result.VALOR_TOTAL_CORRIGIDO).toBe('0.00');
    });

    it('should handle high debt values correctly', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '100000,00', // R$ 100,000.00
        DIAS_EM_ATRASO: '365', // 1 year overdue
        PERCENTUAL_MULTA: 10, // 10%
        PERCENTUAL_JUROS_DIARIOS: 0.001, // 0.1% daily
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '10',
          dailyFee: '0,001',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();

      const expectedPenalty = 100000.0 * 0.1; // 10,000.00
      const expectedInterest = 100000.0 * (0.001 / 100) * 365; // 365.00 (dailyFee is converted from percentage)
      const expectedTotal = 100000.0 + expectedPenalty + expectedInterest; // 110,365.00

      expect(parseFloat(result.VALOR_TOTAL_MULTA)).toBeCloseTo(expectedPenalty, 2);
      expect(parseFloat(result.VALOR_TOTAL_ENCARGOS)).toBeCloseTo(expectedInterest, 2);
      expect(parseFloat(result.VALOR_TOTAL_CORRIGIDO)).toBeCloseTo(expectedTotal, 2);
    });

    it('should handle invalid data gracefully with NaN values', () => {
      // Test with invalid data that will cause parsing to fail
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: 'invalid-number',
        DIAS_EM_ATRASO: 'invalid-number',
        PERCENTUAL_MULTA: 'invalid-number',
        PERCENTUAL_JUROS_DIARIOS: 'invalid-number',
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: 'invalid-number',
          dailyFee: 'invalid-number',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.VALOR_TOTAL_MULTA).toBe('NaN');
      expect(result.VALOR_TOTAL_ENCARGOS).toBe('NaN');
      expect(result.VALOR_TOTAL_CORRIGIDO).toBe('NaN');
    });

    // Flexible Tax Rules Handling Tests
    it('should handle only penalty fee provided (no daily fee)', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        DIAS_EM_ATRASO: '30',
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '2,69', // Only penalty fee provided
          // dailyFee not provided
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.PERCENTUAL_MULTA).toBe(2.69);
      expect(result.PERCENTUAL_JUROS_DIARIOS).toBeUndefined();

      // Mathematical validation - only penalty should be calculated
      const expectedPenalty = 1000.0 * (2.69 / 100); // 26.90
      const expectedInterest = 0; // No daily fee, so no interest
      const expectedTotal = 1000.0 + expectedPenalty + expectedInterest; // 1026.90

      expect(parseFloat(result.VALOR_TOTAL_MULTA)).toBeCloseTo(expectedPenalty, 2);
      expect(parseFloat(result.VALOR_TOTAL_ENCARGOS)).toBeCloseTo(expectedInterest, 2);
      expect(parseFloat(result.VALOR_TOTAL_CORRIGIDO)).toBeCloseTo(expectedTotal, 2);
    });

    it('should handle only daily fee provided (no penalty fee)', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        DIAS_EM_ATRASO: '30',
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          // penaltyFee not provided
          dailyFee: '0,0003333', // Only daily fee provided
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.PERCENTUAL_MULTA).toBeUndefined();
      expect(result.PERCENTUAL_JUROS_DIARIOS).toBe(0.0003333);

      // Mathematical validation - only interest should be calculated
      const expectedPenalty = 0; // No penalty fee, so no penalty
      const expectedInterest = 1000.0 * (0.0003333 / 100) * 30; // ~0.1 (dailyFee is converted from percentage)
      const expectedTotal = 1000.0 + expectedPenalty + expectedInterest; // ~1000.1

      expect(parseFloat(result.VALOR_TOTAL_MULTA)).toBeCloseTo(expectedPenalty, 2);
      expect(parseFloat(result.VALOR_TOTAL_ENCARGOS)).toBeCloseTo(expectedInterest, 2);
      expect(parseFloat(result.VALOR_TOTAL_CORRIGIDO)).toBeCloseTo(expectedTotal, 2);
    });

    it('should handle empty tax rules object', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        DIAS_EM_ATRASO: '30',
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {} as TaxRulesDto, // Empty tax rules
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.PERCENTUAL_MULTA).toBeUndefined();
      expect(result.PERCENTUAL_JUROS_DIARIOS).toBeUndefined();

      // Mathematical validation - no fees, so only original value
      const expectedPenalty = 0;
      const expectedInterest = 0;
      const expectedTotal = 1000.0 + expectedPenalty + expectedInterest; // 1000.00

      expect(parseFloat(result.VALOR_TOTAL_MULTA)).toBeCloseTo(expectedPenalty, 2);
      expect(parseFloat(result.VALOR_TOTAL_ENCARGOS)).toBeCloseTo(expectedInterest, 2);
      expect(parseFloat(result.VALOR_TOTAL_CORRIGIDO)).toBeCloseTo(expectedTotal, 2);
    });

    it('should handle null/undefined tax rule values', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        DIAS_EM_ATRASO: '30',
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: null,
          dailyFee: undefined,
        } as any,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.PERCENTUAL_MULTA).toBeUndefined();
      expect(result.PERCENTUAL_JUROS_DIARIOS).toBeUndefined();

      // Mathematical validation - null/undefined values should result in zero calculations
      const expectedPenalty = 0;
      const expectedInterest = 0;
      const expectedTotal = 1000.0;

      expect(parseFloat(result.VALOR_TOTAL_MULTA)).toBeCloseTo(expectedPenalty, 2);
      expect(parseFloat(result.VALOR_TOTAL_ENCARGOS)).toBeCloseTo(expectedInterest, 2);
      expect(parseFloat(result.VALOR_TOTAL_CORRIGIDO)).toBeCloseTo(expectedTotal, 2);
    });

    // Mathematical Precision Validation Tests
    it('should maintain mathematical precision with complex decimal calculations', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1234,56',
        DIAS_EM_ATRASO: '45',
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '3,75', // 3.75%
          dailyFee: '0,0004167', // 1.25% monthly / 30 days = 0.04167% daily
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();

      // Mathematical validation with exact calculations
      const originalValue = 1234.56;
      const penaltyRate = 3.75 / 100; // 0.0375
      const dailyRate = 0.0004167 / 100; // dailyFee is converted from percentage
      const days = 45;

      const expectedPenalty = originalValue * penaltyRate; // 1234.56 * 0.0375 = 46.296
      const expectedInterest = originalValue * dailyRate * days; // 1234.56 * 0.********* * 45 = ~0.23
      const expectedTotal = originalValue + expectedPenalty + expectedInterest; // 1234.56 + 46.296 + 0.23 = ~1281.09

      expect(parseFloat(result.VALOR_TOTAL_MULTA)).toBeCloseTo(expectedPenalty, 2);
      expect(parseFloat(result.VALOR_TOTAL_ENCARGOS)).toBeCloseTo(expectedInterest, 2);
      expect(parseFloat(result.VALOR_TOTAL_CORRIGIDO)).toBeCloseTo(expectedTotal, 2);

      // Verify 2-decimal precision formatting
      expect(result.VALOR_TOTAL_MULTA).toMatch(/^\d+\.\d{2}$/);
      expect(result.VALOR_TOTAL_ENCARGOS).toMatch(/^\d+\.\d{2}$/);
      expect(result.VALOR_TOTAL_CORRIGIDO).toMatch(/^\d+\.\d{2}$/);
    });

    it('should handle very small decimal values with proper rounding', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '0,01', // 1 cent
        DIAS_EM_ATRASO: '1',
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '0,01', // 0.01%
          dailyFee: '0,0000333', // Very small daily rate
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();

      // For very small values that result in amounts < 0.01, they should be rounded to 0.00
      // This is correct financial behavior - we don't deal with fractions of cents
      expect(result.VALOR_TOTAL_MULTA).toBe('0.00'); // 0.01 * 0.0001 = 0.000001 → rounds to 0.00
      expect(result.VALOR_TOTAL_ENCARGOS).toBe('0.00'); // 0.01 * 0.0000333 = 0.********* → rounds to 0.00
      expect(result.VALOR_TOTAL_CORRIGIDO).toBe('0.01'); // Original value remains

      // Verify 2-decimal precision formatting
      expect(result.VALOR_TOTAL_MULTA).toMatch(/^\d+\.\d{2}$/);
      expect(result.VALOR_TOTAL_ENCARGOS).toMatch(/^\d+\.\d{2}$/);
      expect(result.VALOR_TOTAL_CORRIGIDO).toMatch(/^\d+\.\d{2}$/);
    });
  });

  describe('Recovered Value Functionality', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    describe('getTotalDealValueByCustomer', () => {
      it('should return total recovered value without date filtering', async () => {
        const customerId = 'customer-123';
        const expectedValue = 150000; // 1500.00 in cents

        mockCollectCashStatsAdapter.getTotalDealValueByCustomerId.mockResolvedValue(
          expectedValue,
        );

        const result = await useCase.getTotalDealValueByCustomer(customerId);

        expect(result).toBe(expectedValue);
        expect(mockCollectCashStatsAdapter.getTotalDealValueByCustomerId).toHaveBeenCalledWith(
          customerId,
        );
        expect(
          mockCollectCashStatsAdapter.getTotalDealValueByCustomerIdWithDateRange,
        ).not.toHaveBeenCalled();
      });

      it('should return total recovered value with date filtering', async () => {
        const customerId = 'customer-123';
        const startDate = new Date('2024-01-01');
        const endDate = new Date('2024-12-31');
        const expectedValue = 75000; // 750.00 in cents

        mockCollectCashStatsAdapter.getTotalDealValueByCustomerIdWithDateRange.mockResolvedValue(
          expectedValue,
        );

        const result = await useCase.getTotalDealValueByCustomer(
          customerId,
          startDate,
          endDate,
        );

        expect(result).toBe(expectedValue);
        expect(
          mockCollectCashStatsAdapter.getTotalDealValueByCustomerIdWithDateRange,
        ).toHaveBeenCalledWith(customerId, startDate, endDate);
        expect(
          mockCollectCashStatsAdapter.getTotalDealValueByCustomerId,
        ).not.toHaveBeenCalled();
      });

      it('should return total recovered value with only start date', async () => {
        const customerId = 'customer-123';
        const startDate = new Date('2024-01-01');
        const expectedValue = 100000; // 1000.00 in cents

        mockCollectCashStatsAdapter.getTotalDealValueByCustomerIdWithDateRange.mockResolvedValue(
          expectedValue,
        );

        const result = await useCase.getTotalDealValueByCustomer(customerId, startDate);

        expect(result).toBe(expectedValue);
        expect(
          mockCollectCashStatsAdapter.getTotalDealValueByCustomerIdWithDateRange,
        ).toHaveBeenCalledWith(customerId, startDate, undefined);
      });

      it('should return total recovered value with only end date', async () => {
        const customerId = 'customer-123';
        const endDate = new Date('2024-12-31');
        const expectedValue = 125000; // 1250.00 in cents

        mockCollectCashStatsAdapter.getTotalDealValueByCustomerIdWithDateRange.mockResolvedValue(
          expectedValue,
        );

        const result = await useCase.getTotalDealValueByCustomer(
          customerId,
          undefined,
          endDate,
        );

        expect(result).toBe(expectedValue);
        expect(
          mockCollectCashStatsAdapter.getTotalDealValueByCustomerIdWithDateRange,
        ).toHaveBeenCalledWith(customerId, undefined, endDate);
      });

      it('should handle zero recovered value', async () => {
        const customerId = 'customer-123';
        const expectedValue = 0;

        mockCollectCashStatsAdapter.getTotalDealValueByCustomerId.mockResolvedValue(
          expectedValue,
        );

        const result = await useCase.getTotalDealValueByCustomer(customerId);

        expect(result).toBe(expectedValue);
        expect(mockCollectCashStatsAdapter.getTotalDealValueByCustomerId).toHaveBeenCalledWith(
          customerId,
        );
      });
    });
  });

  describe('Portfolio Workflow Management for Aggregated Exports', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    describe('validateAndGroupPortfoliosByWorkflow', () => {
      it('should return single workflow group when all portfolios have the same workflow', async () => {
        const portfolios = [
          { id: 'portfolio-1', name: 'Portfolio 1', workflowId: 'workflow-123' },
          { id: 'portfolio-2', name: 'Portfolio 2', workflowId: 'workflow-123' },
          { id: 'portfolio-3', name: 'Portfolio 3', workflowId: 'workflow-123' },
        ];

        const result = await (useCase as any).validateAndGroupPortfoliosByWorkflow(portfolios);

        expect(result).toBeInstanceOf(Map);
        expect(result.size).toBe(1);
        expect(result.has('workflow-123')).toBe(true);
        expect(result.get('workflow-123')).toHaveLength(3);
      });

      it('should return single workflow group for single portfolio', async () => {
        const portfolios = [
          { id: 'portfolio-1', name: 'Portfolio 1', workflowId: 'workflow-123' },
        ];

        const result = await (useCase as any).validateAndGroupPortfoliosByWorkflow(portfolios);

        expect(result).toBeInstanceOf(Map);
        expect(result.size).toBe(1);
        expect(result.has('workflow-123')).toBe(true);
        expect(result.get('workflow-123')).toHaveLength(1);
      });

      it('should return empty map for empty portfolio array', async () => {
        const portfolios = [];

        const result = await (useCase as any).validateAndGroupPortfoliosByWorkflow(portfolios);

        expect(result).toBeInstanceOf(Map);
        expect(result.size).toBe(0);
      });

      it('should group portfolios by workflow when multiple workflows exist', async () => {
        const portfolios = [
          { id: 'portfolio-1', name: 'Portfolio 1', workflowId: 'workflow-123' },
          { id: 'portfolio-2', name: 'Portfolio 2', workflowId: 'workflow-456' },
          { id: 'portfolio-3', name: 'Portfolio 3', workflowId: 'workflow-123' },
          { id: 'portfolio-4', name: 'Portfolio 4', workflowId: 'workflow-789' },
        ];

        const result = await (useCase as any).validateAndGroupPortfoliosByWorkflow(portfolios);

        expect(result).toBeInstanceOf(Map);
        expect(result.size).toBe(3);
        expect(result.has('workflow-123')).toBe(true);
        expect(result.has('workflow-456')).toBe(true);
        expect(result.has('workflow-789')).toBe(true);
        expect(result.get('workflow-123')).toHaveLength(2);
        expect(result.get('workflow-456')).toHaveLength(1);
        expect(result.get('workflow-789')).toHaveLength(1);
      });

      it('should correctly group portfolios with complex workflow distribution', async () => {
        const portfolios = [
          { id: 'portfolio-1', name: 'Portfolio 1', workflowId: 'workflow-aaa' },
          { id: 'portfolio-2', name: 'Portfolio 2', workflowId: 'workflow-bbb' },
          { id: 'portfolio-3', name: 'Portfolio 3', workflowId: 'workflow-ccc' },
          { id: 'portfolio-4', name: 'Portfolio 4', workflowId: 'workflow-aaa' },
          { id: 'portfolio-5', name: 'Portfolio 5', workflowId: 'workflow-bbb' },
        ];

        const result = await (useCase as any).validateAndGroupPortfoliosByWorkflow(portfolios);

        expect(result).toBeInstanceOf(Map);
        expect(result.size).toBe(3);
        expect(result.get('workflow-aaa')).toHaveLength(2);
        expect(result.get('workflow-bbb')).toHaveLength(2);
        expect(result.get('workflow-ccc')).toHaveLength(1);
      });
    });
  });
});
