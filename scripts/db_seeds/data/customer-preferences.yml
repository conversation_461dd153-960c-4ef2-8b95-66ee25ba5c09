customerPreferences:
  # DigAI - Main customer with comprehensive preferences
  - customerId: 4cd6d515-2604-4c2c-adad-435acbef1f5c
    portfolio:
      defaultWorkflowId: 6f413811-4aa8-43f4-8c48-d00143dd226d
      timezoneUTC: "-3"
      importCronExpression: "0 9 * * 1-5"
      followUpWorkflowId: 7f413811-4aa8-43f4-8c48-d00143dd226e
      followUpCronExpression: "0 */2 * * *"
      followUpQuantity: 3
      followUpIntervalMinutes: 120
      exportColumns: ["name", "phone", "status", "lastInteraction", "followUpCount"]
      daysBeforePaymentReminder: 3
      paymentReminderInDay: true
      customImportConfig:
        delimiter: ","
        taxRules:
          penaltyFee: "2.69" # percentage (2.69%, will be converted to 0.0269)
          dailyFee: "0.0333" # percentage
        additionalHeaders: [VALOR_DIVIDA_CORRIGIDO]
      statsConfig:
        - workflowName: "digai-negociador-divida-workflow"
          dealValue:
            source: "customData"
            path: "VALOR_DIVIDA_CORRIGIDO"
          originalDebt:
            source: "customData"
            path: "VALOR_DIVIDA_ORIGINAL"
      exportConfig:
        - workflowName: "digai-negociador-divida-workflow"
          "Portfolio Name": 
            source: "portfolio"
            path: "name"
          "Status":
            source: "portfolio-item"
            path: "current_status"
          "Phone Number":
            source: "portfolio-item"
            path: "phone_number"
          "Valor Original da Divida":
            source: "customData"
            path: "VALOR_DIVIDA_ORIGINAL"
          "Referencia da Divida":
            source: "customData"
            path: "REFERENCIA_DIVIDA"
          "Nome do Cliente":
            source: "customData"
            path: "NOME_DO_CLIENTE"
          "CNPJ ou CPF do Cliente":
            source: "customData"
            path: "CNPJ_CPF"
          "Nome da Distribuidora":
            source: "customData"
            path: "NOME_DA_DISTRIBUIDORA"
          "Número da Instalação":
            source: "customData"
            path: "NUMERO_DA_INSTALACAO"
          "Resumo da negociação":
            source: "middleware"
            path: "[resumo-negociacao].resumo"
          "Data do Acionamento":
            source: "portfolio-item"
            path: "sent_at"
            format: "data_hora"