import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { SQSService } from '@common/sqs/sqs.service';
import { MessageTransformerFactory } from '@message-hub/application/factories/message-converter.factory';
import { SendMessageRequestDto } from '@message-hub/application/dto/in/send-message-request.dto';
import { IncomingMessageRequestDto } from '@message-hub/application/dto/in/incoming-message-request.dto';
import { SmsServiceProviderPort } from '@message-hub/infrastructure/ports/http/sms-service-provider.port';
import { CommunicationChannel } from '@common/enums';

import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';
import { IncomingMessageUseCase } from '@message-hub/application/use-cases/incoming-message.use-case';
import { S3Service } from '@common/s3/s3.service';
import { randomUUID } from 'crypto';
import { logger } from '@edutalent/commons-sdk';

@Injectable()
export class MessageUseCase {
  private readonly directMessageFilesBucketName: string;

  constructor(
    private readonly sqsService: SQSService,
    private readonly s3Service: S3Service,
    @Inject('SmsServiceProviderPort')
    readonly smsServiceProviderPort: SmsServiceProviderPort,
    @Inject(forwardRef(() => OutgoingMessageUseCase))
    private readonly outgoingMessageUseCase: OutgoingMessageUseCase,
    private readonly incomingMessageUseCase: IncomingMessageUseCase,
  ) {
    this.directMessageFilesBucketName = process.env.DIRECT_MESSAGE_FILES_BUCKET;
  }

  async sendMessage(sendMessageRequestDto: SendMessageRequestDto): Promise<void> {
    const messageTransformer = MessageTransformerFactory.getMessageTransformer(
      sendMessageRequestDto.communicationChannel,
    );

    const messageOutgoing = messageTransformer.generateOutgoingMessage(sendMessageRequestDto);

    if (sendMessageRequestDto.communicationChannel === CommunicationChannel.LOVELACE) {
      await this.outgoingMessageUseCase.sendMessageLovelaceDirectly(
        sendMessageRequestDto.customerId,
        sendMessageRequestDto.communicationChannel,
        sendMessageRequestDto.to,
        messageOutgoing.message,
      );
      return;
    }

    const queueName = this.sqsService.getOutgoingQueueNameByChannel(
      sendMessageRequestDto.communicationChannel,
    );

    const queueUrl = process.env[queueName];

    await this.sqsService.produce(queueUrl, messageOutgoing);
  }

  async receiveMessage(incomingMessageRequestDto: IncomingMessageRequestDto): Promise<void> {
    if (
      incomingMessageRequestDto.communicationChannel === CommunicationChannel.WHATSAPPSELFHOSTED &&
      (!incomingMessageRequestDto.from.startsWith('55') ||
        incomingMessageRequestDto.from.length > 13)
    ) {
      logger.info(
        `Received message from: ${incomingMessageRequestDto.from} to: ${incomingMessageRequestDto.to} with communication channel: ${incomingMessageRequestDto.communicationChannel} is not a brazilian phone number, discarding...`,
      );
      return;
    }

    let fileUrl = null;

    if (incomingMessageRequestDto.fileBuffer) {
      const fileKey = `${incomingMessageRequestDto.from}/${randomUUID()}.mp3`;
      fileUrl = await this.s3Service.uploadFile(
        this.directMessageFilesBucketName,
        fileKey,
        Buffer.from(incomingMessageRequestDto.fileBuffer),
      );
    }

    // Create SQS message payload without the fileBuffer
    const sqsMessagePayload = {
      from: incomingMessageRequestDto.from,
      to: incomingMessageRequestDto.to,
      messageType: incomingMessageRequestDto.messageType,
      communicationChannel: incomingMessageRequestDto.communicationChannel,
      message: incomingMessageRequestDto.message,
      fileUrl,
    };

    const queueUrl = process.env.INCOMING_MESSAGE_QUEUE_URL;
    await this.sqsService.produce(queueUrl, sqsMessagePayload);
  }

  async reprocessLastMessage(phoneNumber: string): Promise<void> {
    await this.incomingMessageUseCase.reprocessLastMessage(phoneNumber);
  }
}
